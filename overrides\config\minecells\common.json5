{
	"elevator": {
		//  default: 256, min: 64, max: 320
		"maxAssemblyHeight": 256,
		//  default: 1, min: 1, max: 10
		"minAssemblyHeight": 1,
		//  default: 1.0, min: 0.1, max: 2.5
		"speed": 1.0,
		//  default: 0.01, min: 0.001, max: 0.1
		"acceleration": 0.009999999776482582,
		//  default: 10.0, min: 0.0, max: 100.0
		"damage": 10.0
	},
	//  Whether the entry door to boss rooms should remain unlocked
	"unlockedBossEntry": false,
	/*  Forces the teleport between dimensions method to be used from the main server thread
	    Might be necessary when used with mods that mess with threading, like C2ME  (if DEFAULT is set, this option will enable itself if that mod is present)
	    Warning: This may cause weird desync issues, so use at your own risk and only if it crashes without it
	    Possible values: ALWAYS | NEVER | DEFAULT
	*/
	"teleportForceMainThread": "DEFAULT",
	//  Whether the Mine Cells data should automatically get wiped after major updates
	"autoWipeData": false,
	/*  Disable the fall protection feature of the Ramparts. 
	    This will let players explore the bottom of the dimension freely, but keep in mind: 
	    that isn't the intended way to explore the dimension.
	*/
	"disableFallProtection": false,
	/*  The maximum distance (in blocks) the Conjunctivius' Tentacle weapon can stretch when activated
	    Breaks at large distances (above around 64 blocks), so be careful with high values.
	*/
	"baseTentacleMaxDistance": 24,
	// The additional time (in ticks) after holding up a shield that allows you to parry an attack
	"additionalParryTime": 0
}