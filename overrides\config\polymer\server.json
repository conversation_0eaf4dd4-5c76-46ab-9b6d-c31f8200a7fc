{"_c7": "Displays vanilla/modded creatives tabs in /polymer creative", "displayNonPolymerCreativeTabs": true, "_c9": "Makes server send additional block updates around clicked area", "sendBlocksAroundClicked": true, "_c11": "Makes polymer report time it's handshake took", "logHandshakeTime": false, "_c12": "Enables logging of BlockState ids rebuilds", "logBlockStateRebuilds": true, "_c1": "Enables syncing of non-polymer entries as polymer ones, when PolyMc is present", "polyMcSyncModdedEntries": true, "_c2": "Delay from last light updates to syncing it to clients, in ticks", "lightUpdateTickDelay": 1, "_c3": "Forcefully enables strict block updates, making client desyncs less likely to happen", "force_strict_block_updates": false, "_c4": "Enables experimental passing of ItemStack context through nbt, allowing for better mod compat", "item_stack_nbt_hack": true}