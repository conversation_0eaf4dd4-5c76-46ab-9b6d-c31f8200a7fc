{
// --------------------------------------------- LOOT CONFIGURATION ----------------------------------------------------------------- 
// This config allows for the tweaking of loot injection for UNIQUE weapons. 
// Standard iron > diamond gear, and Runic Tablets, are controlled by the loot modifiers in the general_config, not here.  
// ---------------------------------------------------------------------------------------------------------------------------------- 
// --------------------------------------------- How does it work? ------------------------------------------------------------------ 
// If 'add_weapons_to_loot_tables' is enabled in the simplyswords_main config, Simply Swords will attempt to inject its loot into    
// any loot table that contains 'chests' in its filepath. This includes loot tables from other mods.
// Any values provided in this config will override the aforementioned process, acting as both a whitelist, and a blacklist.

// Each line must be made up of a string containing the namespace, path, and filename, in addition to a float value.        
// The float value provided will determine the chance of the loot appearing in the loot table. Where 0.0 is %0, and 1.0 is %100.
// If the float value provided is 0.0 the items will be blacklisted from this loot table and skipped entirely by the loot injection.

// Items can also be injected into entity loot tables, as seen in the Wither example below.
// ---------------------------------------------------------------------------------------------------------------------------------- 

  "minecraft:entities/wither": 0.05,
  "minecraft:entities/ender_dragon": 0.5,
  "minecraft:chests/ruined_portal": 0.0,
  "minecraft:chests/village/village_armorer": 0.0,
  "minecraft:chests/village/village_butcher": 0.0,
  "minecraft:chests/village/village_cartographer": 0.0,
  "minecraft:chests/village/village_desert_house": 0.0,
  "minecraft:chests/village/village_fisher": 0.0,
  "minecraft:chests/village/village_fletcher": 0.0,
  "minecraft:chests/village/village_mason": 0.0,
  "minecraft:chests/village/village_plains_house": 0.0,
  "minecraft:chests/village/village_savanna_house": 0.0,
  "minecraft:chests/village/village_shepard": 0.0,
  "minecraft:chests/village/village_snowy_house": 0.0,
  "minecraft:chests/village/village_taiga_house": 0.0,
  "minecraft:chests/village/village_tannery": 0.0,
  "minecraft:chests/village/village_temple": 0.0,
  "minecraft:chests/village/village_toolsmith": 0.0,
  "minecraft:chests/village/village_weaponsmith": 0.0
}