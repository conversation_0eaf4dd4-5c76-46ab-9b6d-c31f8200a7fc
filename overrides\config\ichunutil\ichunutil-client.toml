# These configs affect GUIs using B&S.
[bns]

	# Renders B&S's GUIs in a Minecraft Style instead.
	# 0 = Default Style
	# 1 = Vanilla Style
	# 2 = Texture Pack Style.
	# Range: 0 ~ 2
	bnsMinecraftStyle = 0

	# How much padding to add to the docked windows.
	# Range: 0 ~ 50
	bnsDockPadding = 0

	# Number of pixels before iChunUtil thinks you're trying to dock a window.
	# Range: > 1
	bnsDockBorder = 8

	# Speed (in ticks) to register a double click.
	# Range: > 1
	bnsDoubleClickSpeed = 10

	# How long to mouse over an element (in ticks) before showing a tooltip.
	# Range: 0 ~ 6000
	bnsTooltipCooldown = 10

	# Theme to use for Boxes & Stuff
	bnsTheme = "default"

