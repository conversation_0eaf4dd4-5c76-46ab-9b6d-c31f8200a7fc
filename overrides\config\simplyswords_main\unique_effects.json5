{"abilityAbsorptionCap": 20.0, "watcherChance": 5.0, "watcherRestoreAmount": 0.5, "watcherRadius": 8.0, "omenChance": 5.0, "omenAbsorptionCap": 20.0, "omenInstantKillThreshold": 0.25, "stealChance": 25.0, "stealDuration": 400.0, "stealInvisDuration": 120.0, "stealBlindDuration": 200.0, "stealRadius": 30.0, "stealSpellScaling": 2.5999999046325684, "soulMeldChance": 75.0, "soulMeldDuration": 250.0, "soulMeldRadius": 5.0, "soulrendChance": 85.0, "soulrendDuration": 500.0, "soulrendDamageMulti": 3.0, "soulrendHealMulti": 0.5, "soulrendRadius": 10.0, "soulrendMaxStacks": 8.0, "soulrendDamageSpellScaling": 0.4000000059604645, "ferocityChance": 75.0, "ferocityDuration": 100.0, "ferocityMaxStacks": 15.0, "ferocityStrengthTier": 2.0, "emberIreChance": 30.0, "emberIreDuration": 150.0, "volcanicFuryChance": 25.0, "volcanicFuryRadius": 3.0, "volcanicFuryCooldown": 300.0, "volcanicFuryDamage": 3.0, "volcanicFurySpellScaling": 1.399999976158142, "stormChance": 15.0, "stormRadius": 10.0, "stormCooldown": 700.0, "stormFrequency": 10.0, "stormDuration": 200.0, "plagueChance": 55.0, "brimstoneChance": 15.0, "brambleChance": 45.0, "soultetherRange": 32.0, "soultetherRadius": 8.0, "soultetherDuration": 120.0, "soultetherIgniteDuration": 120.0, "soultetherResistanceDuration": 60.0, "frostFuryCooldown": 380.0, "frostFuryRadius": 3.0, "frostFuryDamage": 18.0, "frostFuryChance": 15.0, "frostFuryDuration": 80.0, "frostFurySpellScaling": 1.399999976158142, "moltenRoarCooldown": 320.0, "moltenRoarRadius": 5.0, "moltenRoarKnockbackStrength": 5.0, "moltenRoarChance": 15.0, "moltenRoarDuration": 100.0, "frostShatterRadius": 3.0, "frostShatterDamage": 18.0, "frostShatterChance": 15.0, "frostShatterDuration": 80.0, "frostShatterSpellScaling": 1.7000000476837158, "permafrostRadius": 4.0, "permafrostDamage": 1.0, "permafrostCooldown": 600.0, "permafrostDuration": 200.0, "permafrostSpellScaling": 0.8999999761581421, "arcaneAssaultRadius": 6.0, "arcaneAssaultDamage": 1.0, "arcaneAssaultCooldown": 220.0, "arcaneAssaultChance": 25.0, "arcaneAssaultDuration": 120.0, "arcaneAssaultSpellScaling": 1.399999976158142, "thunderBlitzRadius": 2.0, "thunderBlitzDamage": 3.0, "thunderBlitzCooldown": 250.0, "thunderBlitzChance": 15.0, "thunderBlitzSpellScaling": 1.7000000476837158, "stormJoltCooldown": 100.0, "stormJoltChance": 15.0, "soulAnguishRadius": 3.0, "soulAnguishAbsorptionCap": 8.0, "soulAnguishDamage": 4.0, "soulAnguishCooldown": 700.0, "soulAnguishDuration": 200.0, "soulAnguishHeal": 0.5, "soulAnguishRange": 22.0, "soulAnguishSpellScaling": 1.600000023841858, "shockDeflectBlockDuration": 35.0, "shockDeflectDamage": 12.0, "shockDeflectCooldown": 90.0, "shockDeflectParryDuration": 10.0, "shockDeflectSpellScaling": 2.299999952316284, "shadowmistCooldown": 200.0, "shadowmistChance": 25.0, "shadowmistDamageMulti": 0.800000011920929, "shadowmistBlindDuration": 60.0, "shadowmistRadius": 4.0, "abyssalStandardCooldown": 700.0, "abyssalStandardChance": 15.0, "abyssalStandardDamage": 3.0, "abyssalStandardSpellScaling": 1.2000000476837158, "righteousStandardCooldown": 700.0, "righteousStandardChance": 15.0, "righteousStandardDamage": 3.0, "righteousStandardSpellScaling": 1.100000023841858, "righteousStandardSpellScalingHeal": 1.2999999523162842, "fatalFlickerCooldown": 175.0, "fatalFlickerChance": 15.0, "fatalFlickerRadius": 3.0, "fatalFlickerMaxStacks": 99.0, "fatalFlickerDashVelocity": 3.0, "smoulderCooldown": 80.0, "smoulderMaxStacks": 5.0, "smoulderHeal": 15.0, "smoulderSpellScaling": 0.4000000059604645, "waxweaveCooldown": 1200.0, "waxweaveMaxStacks": 3.0, "hivemindCooldown": 60.0, "hivemindDuration": 450.0, "hivemindDamage": 1.100000023841858, "celestialSurgeCooldown": 120.0, "celestialSurgeDuration": 120.0, "celestialSurgeStacks": 6.0, "celestialSurgeDamageModifier": 0.4000000059604645, "celestialSurgeLifestealModifier": 0.10000000149011612, "flickerFuryCooldown": 220.0, "flickerFuryDuration": 40.0, "flickerFuryDamage": 1.0, "vortexDuration": 1200.0, "vortexMaxSize": 30.0, "vortexMaxStacks": 10.0, "vortexSpellScaling": 0.30000001192092896, "voidcallerDuration": 250.0, "voidcallerStartingTickFrequency": 12.0, "voidcallerDamageModifier": 1.0, "voidcallerCorruptionFrequency": 60.0, "voidcallerCorruptionPerTick": 1.0, "voidcallerCorruptionDuration": 1200.0, "voidcallerCorruptionMax": 100.0, "emberstormSpreadCap": 6.0, "emberstormDamage": 5.0, "emberstormDetonationDamage": 15.0, "emberstormCooldown": 980.0, "emberstormMaxHaste": 10.0, "emberstormSpellScaling": 0.4000000059604645, "ribbonwrathCooldown": 40.0, "ribbonwrathResilienceAmplifier": 1.0, "ribbonwrathDamageBonusPercent": 0.949999988079071, "magistormCooldown": 980.0, "magistormDuration": 400.0, "magistormRadius": 4.0, "magistormDamage": 3.0, "magistormRepairChance": 0.25, "magistormSpellScaling": 0.5, "enigmaCooldown": 800.0, "enigmaDecayRate": 2.0, "enigmaChaseRadius": 16.0, "magibladeCooldown": 35.0, "magibladeSonicDistance": 16.0, "magibladeDamageModifier": 0.699999988079071, "magibladeRepelChance": 55.0, "magibladeRepelRadius": 4.0, "magislamCooldown": 140.0, "magislamRadius": 4.0, "magislamDamageModifier": 2.0, "magislamMagicChance": 35.0, "magislamMagicModifier": 0.5, "astralShiftCooldown": 420.0, "astralShiftDuration": 100.0, "astralShiftDamageModifier": 1.0, "astralShiftDamageMax": 300.0, "astralShiftChance": 5.0}