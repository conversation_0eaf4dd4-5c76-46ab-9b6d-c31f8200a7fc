{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "overworld"
	group: ""
	icon: "minecraft:dragon_head"
	id: "51FE38DFB14D1D53"
	order_index: 1
	quest_links: [ ]
	quests: [
		{
			id: "7D6D7B7DD55F344D"
			rewards: [
				{
					id: "3B788C347294148B"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "09B7052A7F5749E6"
					item: "bosses_of_mass_destruction:mob_ward"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found during &9thunderstorms&r."
			tasks: [{
				entity: "adventurez:summoner"
				icon: "minecraft:skeleton_skull"
				id: "354A89017B6C62D7"
				title: "Kill a Summoner"
				type: "kill"
				value: 1L
			}]
			title: "Defeat a &bSummoner"
			x: -6.0d
			y: 8.0d
		}
		{
			id: "1D7CA374B8A56499"
			rewards: [
				{
					id: "46284E400F628FA5"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "1D716EECE6F5977E"
					item: "minecraft:zombie_head"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Splash a &0Chemical X &rpotion on a &2Zombie&r."
			tasks: [{
				entity: "mutantmonsters:mutant_zombie"
				icon: "minecraft:zombie_head"
				id: "0BF6AA3AB78114F2"
				title: "Kill a Mutant Zombie"
				type: "kill"
				value: 1L
			}]
			title: "Defeat a &2Mutant Zombie"
			x: -9.0d
			y: -4.0d
		}
		{
			id: "56409D4CB2AD12CC"
			rewards: [
				{
					id: "33FB2A62A54A9506"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "45A4C725C743952D"
					item: "minecraft:skeleton_skull"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Splash a &0Chemical X &rpotion on a &fSkeleton&r."
			tasks: [{
				entity: "mutantmonsters:mutant_skeleton"
				icon: "minecraft:skeleton_skull"
				id: "7B411CB1E85CBD5D"
				title: "Kill a Mutant Skeleton"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &7Mutant Skeleton"
			x: -9.0d
			y: -1.0d
		}
		{
			id: "30F2E3D5A1BE3470"
			rewards: [
				{
					id: "42C0606C16E38B74"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "6C745539A5FF1B49"
					item: "minecraft:creeper_head"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Splash a &0Chemical X &rpotion on a &aCreeper&r."
			tasks: [{
				entity: "mutantmonsters:mutant_creeper"
				icon: "minecraft:creeper_head"
				id: "7A9383EF3D361114"
				title: "Kill a Mutant Creeper"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &aMutant Creeper"
			x: -9.0d
			y: 2.0d
		}
		{
			id: "4C2E566E7A8CB896"
			rewards: [
				{
					id: "23164941EF06B60F"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "2B9DBC895F23BC4F"
					item: "supplementaries:enderman_head"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Splash a &0Chemical X &rpotion on a &5Enderman&r."
			tasks: [{
				entity: "mutantmonsters:mutant_enderman"
				icon: "supplementaries:enderman_head"
				id: "3002852D2053C6D3"
				title: "Kill a Mutant Enderman"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &5Mutant Enderman"
			x: -9.0d
			y: 5.0d
		}
		{
			id: "61ADCAC9B7A7B56A"
			rewards: [
				{
					id: "52CB42E618A367C0"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "366E5976C6874DA5"
					item: "minecraft:piglin_head"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Feed a &dPig &rwith a &cFermented Spider Eye &rand splash a &0Chemical X &rpotion on it."
			tasks: [{
				entity: "mutantmonsters:spider_pig"
				icon: "minecraft:porkchop"
				id: "6FA5B3416C921715"
				title: "Kill a Spider-Pig"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &dSpider-Pig"
			x: -9.0d
			y: 8.0d
		}
		{
			id: "38E5B11B2A911693"
			rewards: [
				{
					id: "7FE20F28945647F9"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					count: 4
					id: "4C885A40159D704B"
					item: "minecraft:end_crystal"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Build an &3Eye Shrine&r and place the &5Dragon Egg &ron top of it."
			tasks: [{
				entity: "adventurez:the_eye"
				icon: "minecraft:ender_eye"
				id: "24D5B09DCFAA3792"
				title: "Kill The Eye"
				type: "kill"
				value: 1L
			}]
			title: "Defeat &3The Eye"
			x: 3.0d
			y: 5.0d
		}
		{
			id: "126175F571FE459E"
			rewards: [
				{
					id: "0F47D32303123D09"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					count: 3
					id: "1E0713937C393A4D"
					item: "minecells:monsters_eye"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found at the end of &8Prison&r dimension."
			tasks: [{
				entity: "minecells:conjunctivius"
				icon: "minecells:conjunctivius_respawn_rune"
				id: "68AD7BB3957C4697"
				title: "Kill the Conjunctivius"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &3Conjunctivius"
			x: -3.0d
			y: 5.0d
		}
		{
			id: "73F143909AB2350E"
			rewards: [
				{
					id: "20352925BD06582E"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					id: "500E25825EE0BDAD"
					item: "adventurez:shadow_chest"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in &1The Void &rdimension, which is entered via the portal that is generated when you kill &3The Eye&r."
			tasks: [{
				entity: "adventurez:void_shadow"
				icon: "minecraft:dragon_egg"
				id: "4A4CDCC869C09A6C"
				title: "Kill the Void Shadow"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &1Void Shadow"
			x: 3.0d
			y: 8.0d
		}
		{
			id: "41F61A185297DD77"
			rewards: [
				{
					id: "4444B6E0ACFEDB65"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					id: "7B83C41748CC16CC"
					item: "ringsofascension:ring_wither"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Summoned by placing Soul Stone in the T shape with three &8Wither Skulls &ron top."
			tasks: [{
				entity: "minecraft:wither"
				icon: "minecraft:nether_star"
				id: "7937EC034417B965"
				title: "Kill the Wither"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &8Wither"
			x: 3.0d
			y: -4.0d
		}
		{
			id: "48D0007372C9D92E"
			rewards: [
				{
					count: 4
					id: "3720BB6AF689BDD4"
					item: "minecraft:wither_rose"
					type: "item"
				}
				{
					count: 4
					id: "40245230F364BF29"
					item: "bygonenether:soul_stone"
					type: "item"
				}
			]
			shape: "circle"
			subtitle: "Drops from Wither Skeletons"
			tasks: [{
				id: "4883B256ABFF5BD6"
				item: "minecraft:wither_skeleton_skull"
				type: "item"
			}]
			title: "Obtain the &8Wither Skull"
			x: 2.0d
			y: -4.5d
		}
		{
			id: "295E797273DA5E39"
			rewards: [
				{
					id: "11D4830B70BA63B0"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					count: 10
					id: "397DD4FDB76097CE"
					item: "iceandfire:dragon_meal"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in the center island in the &5End &rdimension."
			tasks: [{
				entity: "minecraft:ender_dragon"
				icon: "minecraft:dragon_head"
				id: "5F4483F1F4A3DCBB"
				title: "Kill the Ender Dragon"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &dEnder Dragon"
			x: 3.0d
			y: 2.0d
		}
		{
			id: "052C63106B71B50C"
			rewards: [
				{
					id: "259C4F7164BFAB16"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					id: "35C4388CF50C4258"
					item: "adventurez:gilded_upgrade_smithing_template"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Summoned at a Golem Altar with four &6Gilded Blackstone Shards&r."
			tasks: [{
				entity: "adventurez:blackstone_golem"
				icon: "adventurez:blackstone_golem_heart"
				id: "433E6ADA9603AC15"
				title: "Kill the Blackstone Golem"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &6Blackstone Golem"
			x: 3.0d
			y: -1.0d
		}
		{
			id: "598D9F525214821B"
			rewards: [{
				id: "562EB749038D79B1"
				item: "adventurez:piglin_flag"
				type: "item"
			}]
			shape: "circle"
			subtitle: "Drops from Piglin Brutes."
			tasks: [{
				id: "0CFBC2C96B778446"
				item: "adventurez:gilded_blackstone_shard"
				type: "item"
			}]
			title: "Obtain the &6Blackstone Shard"
			x: 2.0d
			y: -1.5d
		}
		{
			id: "15CF590B48BB013C"
			rewards: [
				{
					id: "23CBE017D659D24D"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					count: 16
					id: "38337DD7E6E797D5"
					item: "minecraft:nether_gold_ore"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Spawns when &6Nether Gold Ore &ris mined."
			tasks: [{
				entity: "adventurez:piglin_beast"
				icon: "minecraft:piglin_head"
				id: "71FA97F4D896CBFD"
				title: "Kill the Piglin Beast"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &6Piglin Beast"
			x: -6.0d
			y: -1.0d
		}
		{
			id: "09616726340BCCC4"
			rewards: [
				{
					id: "7CC602229DF08C26"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					count: 16
					id: "1839CEE661E00EBE"
					item: "minecraft:blaze_rod"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found near &6blaze &rspawners."
			tasks: [{
				entity: "adventurez:blaze_guardian"
				icon: "minecraft:blaze_rod"
				id: "25ED20C66E91945A"
				title: "Kill the Blaze Guardian"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &eBlaze Guardian"
			x: -6.0d
			y: 2.0d
		}
		{
			id: "35BAE4D48A44C9AF"
			rewards: [
				{
					id: "39589F62ECA2B2B7"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					count: 4
					id: "09A155FCC468C18A"
					item: "minecraft:ancient_debris"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in &4Nether Fortress&r."
			tasks: [{
				entity: "adventurez:necromancer"
				icon: "minecraft:wither_rose"
				id: "4920DD09C0260D0D"
				title: "Kill the Necromancer"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &8Necromancer"
			x: -6.0d
			y: 5.0d
		}
		{
			id: "5571C9B8D13BBECA"
			rewards: [
				{
					id: "711724A9DB6B2E92"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					count: 4
					id: "554B60460A9B6AC0"
					item: "deeperdarker:ancient_vase"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Rare chance of spawning after breaking an &8Ancient Vase&r."
			tasks: [{
				entity: "deeperdarker:stalker"
				icon: "deeperdarker:soul_crystal"
				id: "7ECFAA669185E61B"
				title: "Kill the Stalker"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &3Stalker"
			x: -3.0d
			y: -1.0d
		}
		{
			id: "4CB559167C292602"
			rewards: [
				{
					id: "2986CF26EE537415"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "3A51856FFCAB5C63"
					item: "deeperdarker:reinforced_echo_shard"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found underground in the &aOverworld&r."
			tasks: [{
				entity: "minecraft:warden"
				icon: "deeperdarker:heart_of_the_deep"
				id: "137D94883C50CC0B"
				title: "Kill the Warden"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &bWarden"
			x: -3.0d
			y: -4.0d
		}
		{
			id: "1A2E90DF098878DA"
			rewards: [
				{
					id: "21AEE9631DB1BD93"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					count: 16
					id: "0853A2C9B780D1DB"
					item: "oceansdelight:cabbage_wrapped_elder_guardian"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in &bOcean Monuments&r."
			tasks: [{
				entity: "minecraft:elder_guardian"
				icon: "oceansdelight:guardian"
				id: "6FE7A3AB08B471A2"
				title: "Kill the Elder Guardian"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &bElder Guardian"
			x: -6.0d
			y: -4.0d
		}
		{
			icon: "soulsweapons:essence_of_eventide"
			id: "09A92E7BEEC4E6AA"
			rewards: [
				{
					id: "53416EDB272C8E0C"
					type: "xp_levels"
					xp_levels: 20
				}
				{
					id: "696E0A340E461F4E"
					item: {
						Count: 1
						id: "iceandfire:dragonsteel_fire_helmet"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in the &eOld Champion's Graves &rand after killing it, the &1Frenzied Shade &rwill spawn."
			tasks: [
				{
					entity: "soulsweapons:draugr_boss"
					icon: {
						Count: 1
						id: "soulsweapons:draugr"
						tag: {
							Damage: 0
						}
					}
					id: "11157B94A0F3E459"
					title: "Kill the Old Champion's Remains"
					type: "kill"
					value: 1L
				}
				{
					entity: "soulsweapons:night_shade"
					icon: "soulsweapons:essence_of_eventide"
					id: "730F7E410E28B6A4"
					title: "Kill the Frenzied Shade"
					type: "kill"
					value: 1L
				}
			]
			title: "Defeat the &eOld Champion's Remains"
			x: 9.0d
			y: -1.0d
		}
		{
			id: "150BC8C1D2023EDF"
			rewards: [
				{
					id: "175436347E8F1DE6"
					type: "xp_levels"
					xp_levels: 20
				}
				{
					id: "203D5989A480179E"
					item: {
						Count: 1
						id: "iceandfire:dragonsteel_fire_axe"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in the remnants of the &4Decaying Kingdom &rin the &4Nether&r."
			tasks: [{
				entity: "soulsweapons:accursed_lord_boss"
				icon: "soulsweapons:withered_demon_heart"
				id: "7A7B0F45141C8452"
				title: "Kill the Decaying King"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &4Decaying King"
			x: 9.0d
			y: -4.0d
		}
		{
			id: "6F42DAA44109E5BA"
			rewards: [
				{
					id: "2CC2FAF339723DAE"
					type: "xp_levels"
					xp_levels: 20
				}
				{
					id: "142656331133ED3B"
					item: {
						Count: 1
						id: "iceandfire:dragonsteel_fire_chestplate"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Summoned by using the &bEssence of Eventide &ron the &bOld Moon Altar&r."
			tasks: [{
				entity: "soulsweapons:moonknight"
				icon: "soulsweapons:essence_of_luminescence"
				id: "605B240EAE217FA2"
				title: "Kill the Fallen Icon"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &bFallen Icon"
			x: 9.0d
			y: 2.0d
		}
		{
			id: "4657158C259EC82C"
			rewards: [
				{
					id: "43F01880E01D9F8C"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					count: 4
					id: "63C5B3AFF1E5858D"
					item: "aquamirae:abyssal_amethyst"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Summoned by using the &6Shell Horn &rnear water in an &bIce Maze&r."
			tasks: [{
				entity: "aquamirae:captain_cornelia"
				icon: {
					Count: 1
					id: "aquamirae:three_bolt_helmet"
					tag: {
						Damage: 0
					}
				}
				id: "39277C96F2B9EA6B"
				title: "Kill the Ghost of Captain Cornelia"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &5Ghost of the Captain Cornelia"
			x: 0.0d
			y: -4.0d
		}
		{
			id: "105D788A676D7396"
			rewards: [
				{
					id: "532C574BFBA6B7C5"
					type: "xp_levels"
					xp_levels: 20
				}
				{
					id: "1EB0D7D606705A06"
					item: {
						Count: 1
						id: "iceandfire:dragonsteel_fire_leggings"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Summoned by using the &8Shard of Uncertainty &ron the &3Blackstone Pedestal block&r."
			tasks: [{
				entity: "soulsweapons:chaos_monarch"
				icon: {
					Count: 1
					id: "soulsweapons:chaos_crown"
					tag: {
						Damage: 0
					}
				}
				id: "7F16407F294776F9"
				title: "Kill the Monarch of Chaos"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &3Monarch Of Chaos"
			x: 9.0d
			y: 5.0d
		}
		{
			icon: "soulsweapons:chaos_orb"
			id: "0F071E53870E657E"
			rewards: [
				{
					id: "1DEA2D692E08EB60"
					type: "xp_levels"
					xp_levels: 20
				}
				{
					id: "7429B1BF2166601C"
					item: {
						Count: 1
						id: "iceandfire:dragonsteel_fire_boots"
						tag: {
							Damage: 0
						}
					}
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Summoned by throwing the &bChaos Orb &rinto the sky."
			tasks: [
				{
					entity: "soulsweapons:day_stalker"
					icon: {
						Count: 1
						id: "soulsweapons:empowered_dawnbreaker"
						tag: {
							Damage: 0
						}
					}
					id: "61508C7261C0FB93"
					title: "Kill the Day Stalker"
					type: "kill"
					value: 1L
				}
				{
					entity: "soulsweapons:night_prowler"
					icon: {
						Count: 1
						id: "soulsweapons:soul_reaper"
						tag: {
							Damage: 0
						}
					}
					id: "6CDE1D9FE413AE80"
					title: "Kill the Night Prowler"
					type: "kill"
					value: 1L
				}
			]
			title: "Defeat the &6Day Stalker &rand &dNight Prowler"
			x: 9.0d
			y: 8.0d
		}
		{
			id: "7E7DED0265D299C0"
			rewards: [
				{
					id: "22C3B04DEE39F222"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					count: 16
					id: "5F8560CBF4C483EF"
					item: "graveyard:dark_iron_ingot"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "At night, place the &fbone staff pieces &rin the Lich Prison structure, then pour the &4Vial of Blood &rinto the altar."
			tasks: [{
				entity: "graveyard:lich"
				icon: "graveyard:corruption"
				id: "197B60ADA251A1DA"
				title: "Kill the Corrupted Champion"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &8Corrupted Champion"
			x: -3.0d
			y: 8.0d
		}
		{
			id: "1719D39376DB42AD"
			rewards: [
				{
					id: "3E5D73EC1EC352E7"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					count: 8
					id: "01B57E795E37E3C0"
					item: "minecells:cell_infused_steel"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in the &6Ramparts&r at the end of the &3Promenade&r."
			tasks: [{
				entity: "minecells:concierge"
				icon: "minecells:concierge_respawn_rune"
				id: "1696A89FAADE3890"
				title: "Kill the Concierge"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &6Concierge"
			x: -3.0d
			y: 2.0d
		}
		{
			id: "2B565250303715CC"
			shape: "circle"
			subtitle: "Drops from the captain of a pillager ship."
			tasks: [{
				id: "393657DF538282DD"
				item: "aquamirae:shell_horn"
				type: "item"
			}]
			title: "Obtain the &6Shell Horn"
			x: -1.0d
			y: -4.5d
		}
		{
			id: "45564E7F3880CB9F"
			rewards: [
				{
					id: "48D5B3503DF41DFD"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "1BC948A54DF80AA9"
					item: "twilightforest:naga_courtyard_miniature_structure"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			tasks: [{
				advancement: "twilightforest:progress_naga"
				criterion: ""
				icon: "twilightforest:naga_trophy"
				id: "5E0A4B2FF3F7E931"
				title: "Kill the Naga"
				type: "advancement"
			}]
			title: "Defeat the &2Naga"
			x: 6.0d
			y: -4.0d
		}
		{
			id: "45C409B807FDE0F8"
			rewards: [
				{
					id: "4C34E7287057AC86"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "2AF315D64286EB98"
					item: "twilightforest:lich_tower_miniature_structure"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			tasks: [{
				advancement: "twilightforest:progress_lich"
				criterion: ""
				icon: "twilightforest:lich_trophy"
				id: "2B4DE2878BEBEBFA"
				title: "Kill the Twilight Lich"
				type: "advancement"
			}]
			title: "Defeat the &eTwilight Lich"
			x: 6.0d
			y: -1.0d
		}
		{
			id: "4E37793BB88B51CE"
			rewards: [
				{
					id: "6E6C01C88FB6EFC8"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "2E08D9DAE3889839"
					item: "twilightforest:charm_of_life_2"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			tasks: [{
				advancement: "twilightforest:progress_glacier"
				criterion: ""
				id: "29E5B2F12C832525"
				title: "Kill the Snow Queen"
				type: "advancement"
			}]
			title: "Defeat the &bSnow Queen"
			x: 6.0d
			y: 2.0d
		}
		{
			id: "55AF04036E98C908"
			rewards: [
				{
					id: "486F195D77298966"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "71E3F5A4AD43F6BC"
					item: "twilightforest:arctic_fur_block"
					type: "item"
				}
			]
			shape: "circle"
			tasks: [{
				advancement: "twilightforest:progress_yeti"
				criterion: ""
				icon: "twilightforest:alpha_yeti_trophy"
				id: "6D1177EC057BFDD4"
				title: "Kill the Alpha Yeti"
				type: "advancement"
			}]
			title: "Defeat the &9Alpha Yeti"
			x: 5.0d
			y: 1.5d
		}
		{
			id: "3E5939DF3ADD3173"
			rewards: [
				{
					id: "6102E937F730E7B0"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "1E5C9D33E715C3AB"
					item: "twilightforest:knightmetal_block"
					type: "item"
				}
			]
			shape: "circle"
			tasks: [{
				advancement: "twilightforest:progress_knights"
				criterion: ""
				id: "5F70F37B33B6DBE4"
				title: "Kill the Phantom Knight"
				type: "advancement"
			}]
			title: "Defeat the &2Phantom Knight"
			x: 5.0d
			y: 4.5d
		}
		{
			id: "2B9837872F0F440E"
			rewards: [
				{
					id: "256F57B0E3B531D0"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "22A4E0F6D23E9AF5"
					item: "twilightforest:fiery_block"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			tasks: [{
				advancement: "twilightforest:progress_hydra"
				criterion: ""
				id: "6ED7825779423823"
				title: "Kill the Hydra"
				type: "advancement"
			}]
			title: "Defeat the &3Hydra"
			x: 6.0d
			y: 8.0d
		}
		{
			id: "035CBC1F77004607"
			rewards: [
				{
					id: "4737B19F18F11B84"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "4A20677C36BFC5C5"
					item: "twilightforest:uncrafting_table"
					type: "item"
				}
			]
			shape: "circle"
			tasks: [{
				advancement: "twilightforest:progress_labyrinth"
				criterion: ""
				icon: "twilightforest:minoshroom_trophy"
				id: "3D5EC48059A2CE4E"
				title: "Kill the Minoshroom"
				type: "advancement"
			}]
			title: "Defeat the &cMinoshroom"
			x: 5.0d
			y: 7.5d
		}
		{
			id: "176759BF3FE14301"
			rewards: [
				{
					id: "0266DF10204DDE62"
					type: "xp_levels"
					xp_levels: 5
				}
				{
					id: "0CCF2EBE606D7DD4"
					item: "twilightforest:carminite_block"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			tasks: [{
				advancement: "twilightforest:progress_ur_ghast"
				criterion: ""
				id: "7DF6BA328B01D7E0"
				title: "Kill the Ur-Ghast"
				type: "advancement"
			}]
			title: "Defeat the &4Ur-Ghast"
			x: 6.0d
			y: 5.0d
		}
		{
			id: "06AC86A48ADBE461"
			rewards: [
				{
					id: "40F97B4A1CE5DBCB"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					id: "69E660617F5F14D8"
					item: "bosses_of_mass_destruction:void_lily"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in &3cold &rbiomes. "
			tasks: [{
				advancement: "bosses_of_mass_destruction:adventure/night_lich_defeat"
				criterion: ""
				icon: "bosses_of_mass_destruction:ancient_anima"
				id: "2DE4A5899D8481B4"
				title: "Kill the Night Lich"
				type: "advancement"
			}]
			title: "Defeat the &bNight Lich"
			x: 0.0d
			y: -1.0d
		}
		{
			id: "69B398D05F7DE766"
			rewards: [
				{
					id: "3FB016A79DA7AE50"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					count: 16
					id: "0044F43BB3405D3D"
					item: "bosses_of_mass_destruction:crystal_fruit"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in rare caves at the bottom of the &aOverworld&r. &dVoid Lillies&r will point towards the &5Void Blossom&r."
			tasks: [{
				advancement: "bosses_of_mass_destruction:adventure/void_blossom_defeat"
				criterion: ""
				icon: "bosses_of_mass_destruction:void_thorn"
				id: "261068A6FE5B60FA"
				title: "Kill the Void Blossom"
				type: "advancement"
			}]
			title: "Defeat the &dVoid Blossom"
			x: 0.0d
			y: 2.0d
		}
		{
			id: "32FB07636206507B"
			rewards: [
				{
					id: "503F6489D116AAE8"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					id: "355D7C7A6DC3C4E5"
					item: "minecraft:netherite_ingot"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in the &4Arena &rin the &4Nether&r."
			tasks: [{
				advancement: "bosses_of_mass_destruction:nether/gauntlet_defeat"
				criterion: ""
				icon: "bosses_of_mass_destruction:blazing_eye"
				id: "18A25E50B88EB587"
				title: "Kill the Nether Gauntlet"
				type: "advancement"
			}]
			title: "Defeat the &cNether Gauntlet"
			x: 0.0d
			y: 5.0d
		}
		{
			id: "032017DC17C90F12"
			rewards: [
				{
					id: "120838CBCBA3F7DE"
					type: "xp_levels"
					xp_levels: 10
				}
				{
					id: "0F7E808C276B0D85"
					item: "bosses_of_mass_destruction:monolith_block"
					type: "item"
				}
			]
			shape: "octagon"
			size: 2.0d
			subtitle: "Found in the &5Arena &rin the &5End&r."
			tasks: [{
				advancement: "bosses_of_mass_destruction:end/obsidilith_defeat"
				criterion: ""
				icon: "bosses_of_mass_destruction:obsidian_heart"
				id: "5DC8BD0E56F55FFB"
				title: "Kill the Obsidilith"
				type: "advancement"
			}]
			title: "Defeat the &1Obsidilith"
			x: 0.0d
			y: 8.0d
		}
		{
			id: "46287F57E43A09B0"
			rewards: [{
				id: "5B88BD632867EC6A"
				item: {
					Count: 1
					id: "twilightforest:zombie_skull_candle"
					tag: {
						BlockEntityTag: {
							CandleAmount: 1
							CandleColor: 0
						}
					}
				}
				type: "item"
			}]
			shape: "circle"
			subtitle: "Drops from undead mobs."
			tasks: [{
				id: "291E447BB3FF96AD"
				item: "bosses_of_mass_destruction:soul_star"
				type: "item"
			}]
			title: "Obtain the &bSoul Star"
			x: -1.0d
			y: -1.5d
		}
	]
	title: " &9Bosses"
}
