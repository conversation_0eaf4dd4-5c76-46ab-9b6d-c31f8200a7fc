{
	default_hide_dependency_lines: false
	default_quest_shape: ""
	filename: "ice_and_fire"
	group: ""
	icon: "iceandfire:dragon_skull_fire"
	id: "7839558D4A14FA9B"
	order_index: 2
	quest_links: [ ]
	quests: [
		{
			id: "75EC2B083CBC78A9"
			rewards: [
				{
					count: 8
					id: "2F80A691DDB27C01"
					item: "iceandfire:dragonscales_red"
					type: "item"
				}
				{
					count: 2
					id: "2600D9C54FD5C29E"
					item: "iceandfire:witherbone"
					type: "item"
				}
				{
					id: "2C582E6CED630CDC"
					item: "iceandfire:fire_dragon_blood"
					type: "item"
				}
			]
			tasks: [{
				entity: "iceandfire:fire_dragon"
				icon: "iceandfire:fire_dragon_heart"
				id: "5A49C2E379D14F22"
				title: "Kill a Fire Dragon"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &cFire Dragon"
			x: 7.5d
			y: 4.5d
		}
		{
			id: "6773CCD88EBD781E"
			rewards: [
				{
					count: 8
					id: "56863722531A663B"
					item: "iceandfire:dragonscales_blue"
					type: "item"
				}
				{
					count: 2
					id: "7BF6F388F9D13E6B"
					item: "iceandfire:witherbone"
					type: "item"
				}
				{
					id: "2929CC9028599DF9"
					item: "iceandfire:ice_dragon_blood"
					type: "item"
				}
			]
			tasks: [{
				entity: "iceandfire:ice_dragon"
				icon: "iceandfire:ice_dragon_heart"
				id: "5E655191DEE2FFBB"
				title: "Kill an Ice Dragon"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &bIce Dragon"
			x: 9.5d
			y: 4.5d
		}
		{
			id: "465373D30CFA27B1"
			rewards: [
				{
					count: 8
					id: "0238BC96D48497CA"
					item: "iceandfire:dragonscales_amethyst"
					type: "item"
				}
				{
					count: 2
					id: "45899AD533739A47"
					item: "iceandfire:witherbone"
					type: "item"
				}
				{
					id: "5DDEDE82E14A12AC"
					item: "iceandfire:lightning_dragon_blood"
					type: "item"
				}
			]
			tasks: [{
				entity: "iceandfire:lightning_dragon"
				icon: "iceandfire:lightning_dragon_heart"
				id: "7E3E7B9C323C09D5"
				title: "Kill a Lightning Dragon"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &5Lightning Dragon"
			x: 11.5d
			y: 4.5d
		}
		{
			id: "04CF3217DD5F7B29"
			rewards: [{
				count: 16
				id: "7F01B82F7F6ABA5F"
				item: "iceandfire:hydra_arrow"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:hydra"
				icon: "iceandfire:hydra_heart"
				id: "55A90209C9C9BD81"
				title: "Kill a Hydra"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &2Hydra"
			x: 13.5d
			y: 4.5d
		}
		{
			id: "5B2D2C8E955E9C94"
			rewards: [{
				count: 64
				id: "0186A6B8AF09B872"
				item: "minecraft:stone"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:gorgon"
				icon: {
					Count: 1
					id: "iceandfire:gorgon_head"
					tag: {
						Damage: 0
					}
				}
				id: "44E35EB08D6C5B37"
				title: "Kill a Gorgon"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &2Gorgon"
			x: 5.5d
			y: 6.5d
		}
		{
			id: "28ABCEAF88CCCE7E"
			rewards: [{
				id: "67207BB6B972FC59"
				item: "iceandfire:deathworm_tounge"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:deathworm"
				icon: {
					Count: 1
					id: "iceandfire:deathworm_gauntlet_yellow"
					tag: {
						Damage: 0
					}
				}
				id: "1CC22F0992453B80"
				title: "Kill a Death Worm"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &eDeath Worm"
			x: 11.5d
			y: 8.5d
		}
		{
			id: "31EEDAB8CFD7DEFD"
			rewards: [{
				count: 8
				id: "51D9C1F007B09B5A"
				item: "iceandfire:sea_serpent_fang"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:sea_serpent"
				icon: "iceandfire:sea_serpent_fang"
				id: "4C09F0D131304568"
				title: "Kill a Sea Serpent"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &9Sea Serpent"
			x: 11.5d
			y: 6.5d
		}
		{
			id: "23072BDF8ACBF124"
			rewards: [{
				id: "7459E8C5AD30607A"
				item: "iceandfire:siren_tear"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:siren"
				icon: {
					Count: 1
					id: "iceandfire:siren_flute"
					tag: {
						Damage: 0
					}
				}
				id: "76A8E8CA19014877"
				title: "Kill a Siren"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &dSiren"
			x: 13.5d
			y: 6.5d
		}
		{
			id: "432531505CED8AAC"
			rewards: [{
				count: 4
				id: "04BD534DAB4F590D"
				item: "iceandfire:stymphalian_feather_bundle"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:stymphalian_bird"
				icon: "iceandfire:stymphalian_bird_feather"
				id: "35A3FA48DF51FF89"
				title: "Kill a Stymphalian Bird"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &6Sty,phalian Bird"
			x: 13.5d
			y: 8.5d
		}
		{
			id: "642E92A990FD2269"
			rewards: [{
				id: "588C53147CFD1B7E"
				item: "iceandfire:ghost_ingot"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:ghost"
				icon: "iceandfire:ectoplasm"
				id: "4DADD70295C5B3DF"
				title: "Kill a Ghost"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &aGhost"
			x: 5.5d
			y: 8.5d
		}
		{
			id: "11ADA69240CD7963"
			rewards: [{
				id: "2479E205F8FB449C"
				item: {
					Count: 1
					id: "iceandfire:cockatrice_scepter"
					tag: {
						Damage: 0
					}
				}
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:cockatrice"
				icon: "iceandfire:cockatrice_eye"
				id: "1C969E1403BCCEC4"
				title: "Kill a Cockatrice"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &7Cockatrice"
			x: 9.5d
			y: 8.5d
		}
		{
			id: "588B71CCC51D377B"
			rewards: [{
				count: 4
				id: "1CC2E6584C026500"
				item: "iceandfire:troll_tusk"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:troll"
				icon: "iceandfire:troll_skull"
				id: "678C3CFE9EDAA805"
				title: "Kill a Troll"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &8Troll"
			x: 7.5d
			y: 8.5d
		}
		{
			id: "498F1D1F8A43D712"
			rewards: [{
				count: 32
				id: "1B6A49678D5B62DC"
				item: "minecraft:mutton"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:cyclops"
				icon: {
					Count: 1
					id: "iceandfire:cyclops_eye"
					tag: {
						Damage: 0
					}
				}
				id: "1F683F5A13F0EEF3"
				title: "Kill a Cyclops"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &6Cyclops"
			x: 7.5d
			y: 6.5d
		}
		{
			id: "13AEA9AE5CB4DC8C"
			rewards: [{
				id: "13F74B65C899A229"
				item: "iceandfire:myrmex_jungle_swarm"
				type: "item"
			}]
			tasks: [{
				entity: "iceandfire:myrmex_queen"
				icon: "iceandfire:myrmex_stinger"
				id: "7937EB20CD333843"
				title: "Kill a Myrmex Queen"
				type: "kill"
				value: 1L
			}]
			title: "Defeat the &3Myrmex Queen"
			x: 9.5d
			y: 6.5d
		}
		{
			id: "0BE82BCDF56EC743"
			rewards: [{
				count: 12
				id: "3803EF44E04524CE"
				item: "iceandfire:pixie_dust_milky_tea"
				type: "item"
			}]
			tasks: [{
				advancement: "iceandfire:iceandfire/tame_pixie"
				criterion: ""
				id: "6720FE103A341883"
				title: "Befriend a Pixie by dropping a cake near it"
				type: "advancement"
			}]
			title: "Befriend the &dPixie"
			x: 13.5d
			y: 10.5d
		}
		{
			id: "00FEDAD7646973F6"
			rewards: [{
				count: 4
				id: "6772399B14DF3DB6"
				item: "iceandfire:amphithere_feather"
				type: "item"
			}]
			tasks: [{
				advancement: "iceandfire:iceandfire/tame_amphithere"
				criterion: ""
				id: "08A887F189A318B7"
				title: "Tame an Amphithere by shooting it down and riding it"
				type: "advancement"
			}]
			title: "Tame the &3Amphithere"
			x: 9.5d
			y: 10.5d
		}
		{
			id: "216DF7FDC6BC9804"
			rewards: [{
				id: "51FD1B698FE2EFF2"
				item: "iceandfire:hippocampus_fin"
				type: "item"
			}]
			tasks: [{
				advancement: "iceandfire:iceandfire/tame_hippocampus"
				criterion: ""
				id: "22AA698F6547D67A"
				title: "Tame a Hippocampus by feeding it kelp"
				type: "advancement"
			}]
			title: "Tame the &bHippocampus"
			x: 11.5d
			y: 10.5d
		}
		{
			id: "46E459DE7B6CC44C"
			rewards: [{
				id: "2535A6086D865BDF"
				item: "iceandfire:diamond_hippogryph_armor"
				type: "item"
			}]
			tasks: [{
				advancement: "iceandfire:iceandfire/tame_hippogryph"
				criterion: ""
				id: "46726BEBDF4381D7"
				title: "Tame a Hippogryph by dropping rabbit feet near it"
				type: "advancement"
			}]
			title: "Tame the &6Hippogryph"
			x: 7.5d
			y: 10.5d
		}
		{
			id: "3E970AD9C6CCA6D6"
			rewards: [{
				count: 12
				id: "2A5AE119D9B5B421"
				item: "iceandfire:manuscript"
				type: "item"
			}]
			tasks: [{
				id: "1011BA3513CF31CE"
				item: "iceandfire:bestiary"
				title: "Create a Bestiary to Learn about Mythical Creatures"
				type: "item"
			}]
			title: "Craft the &4Bestiary"
			x: 5.5d
			y: 2.5d
		}
		{
			dependencies: ["3E970AD9C6CCA6D6"]
			id: "486AA4FBE4522E2F"
			rewards: [{
				count: 12
				id: "4B63B9CCE3CCD505"
				item: "iceandfire:manuscript"
				type: "item"
			}]
			tasks: [{
				id: "37B4A9B81D311C78"
				item: "iceandfire:lectern"
				title: "Create a Bestiary Lectern and Upgrade your Bestiary"
				type: "item"
			}]
			title: "Craft the &4Bestiary Lectern"
			x: 7.5d
			y: 2.5d
		}
		{
			id: "14796B526F586AA6"
			rewards: [{
				id: "5AC95B37A46DA9B7"
				item: "minecraft:netherite_ingot"
				type: "item"
			}]
			tasks: [{
				id: "38065FAF90289502"
				item: "iceandfire:dragon_seeker"
				title: "Create a Dragon Seeker and use it to Locate Dragons"
				type: "item"
			}]
			title: "Craft the &1Dragon Seeker"
			x: 5.5d
			y: 4.5d
		}
		{
			dependencies: ["486AA4FBE4522E2F"]
			id: "75D90880B2EEF70A"
			rewards: [{
				count: 4
				id: "3A578A38FB32DCA7"
				item: "iceandfire:dragon_meal"
				type: "item"
			}]
			tasks: [{
				id: "4BBAE5C62024A964"
				item: "iceandfire:dragon_stick"
				title: "Create a Dragon Command Staff to Control Your Dragon"
				type: "item"
			}]
			title: "Craft the &7Dragon Command Staff"
			x: 9.5d
			y: 2.5d
		}
		{
			dependencies: ["75D90880B2EEF70A"]
			id: "2DB2201D7F9FF383"
			rewards: [{
				count: 4
				id: "23FB82A8A537857B"
				item: "iceandfire:dragon_meal"
				type: "item"
			}]
			tasks: [{
				id: "2329478DDEFCD37C"
				item: "iceandfire:dragon_horn"
				title: "Create a Dragon Horn to Store Dragons"
				type: "item"
			}]
			title: "Craft the &7Dragon Horn"
			x: 11.5d
			y: 2.5d
		}
		{
			dependencies: ["2DB2201D7F9FF383"]
			id: "6FECF5E02FC55FCD"
			rewards: [{
				count: 4
				id: "0F0884C4575A8D54"
				item: "iceandfire:dragon_meal"
				type: "item"
			}]
			tasks: [{
				id: "63E2B6C78D8343C1"
				item: "iceandfire:dragon_flute"
				title: "Create a Dragon Flute to Stop Runaway Dragons"
				type: "item"
			}]
			title: "Craft the &7Dragon Flute"
			x: 13.5d
			y: 2.5d
		}
		{
			id: "6D45D2D6E52F9FD3"
			rewards: [{
				count: 12
				id: "468E38017A6D802B"
				item: "iceandfire:dragon_meal"
				type: "item"
			}]
			tasks: [{
				advancement: "iceandfire:iceandfire/dragon_egg"
				criterion: ""
				id: "30EB768C3BFEB9F6"
				title: "Obtain a Dragon Egg"
				type: "advancement"
			}]
			title: "Obtain the &4Dragon Egg"
			x: 5.5d
			y: 10.5d
		}
		{
			dependencies: ["1EFEB5A9C1391220"]
			id: "6BB536E4647E981D"
			rewards: [
				{
					count: 12
					id: "470AEB536F3B4803"
					item: "iceandfire:fire_dragon_blood"
					type: "item"
				}
				{
					count: 12
					id: "6AB6EB7B77384975"
					item: "iceandfire:ice_dragon_blood"
					type: "item"
				}
				{
					count: 12
					id: "7F332C0F3DC35A41"
					item: "iceandfire:lightning_dragon_blood"
					type: "item"
				}
			]
			tasks: [{
				advancement: "iceandfire:iceandfire/dragonsteel"
				criterion: ""
				id: "6E4331DAF6036DD5"
				title: "Create a Dragonsteel Ingot from Dragon Blood and Iron"
				type: "advancement"
			}]
			title: "Craft the &4Dragonsteel Ingot"
			x: 11.5d
			y: 12.5d
		}
		{
			dependencies: ["3E8B3AC5607CD59A"]
			id: "2895F13A9C99D578"
			rewards: [
				{
					count: 5
					id: "58EFC832B1556CE5"
					item: "iceandfire:dragonforge_fire_brick"
					type: "item"
				}
				{
					count: 5
					id: "4A70FB9B60A39738"
					item: "iceandfire:dragonforge_ice_brick"
					type: "item"
				}
				{
					count: 5
					id: "60D7675C7D0B28A2"
					item: "iceandfire:dragonforge_lightning_brick"
					type: "item"
				}
			]
			tasks: [{
				advancement: "iceandfire:iceandfire/dragon_forge_brick"
				criterion: ""
				id: "06293046B24C455D"
				title: "Create a Dragon Forge Brick"
				type: "advancement"
			}]
			title: "Craft the &cDragon Forge Brick"
			x: 7.5d
			y: 12.5d
		}
		{
			id: "3E8B3AC5607CD59A"
			rewards: [{
				count: 4
				id: "47F7CB5BA980D779"
				item: "minecraft:netherite_scrap"
				type: "item"
			}]
			tasks: [{
				advancement: "iceandfire:iceandfire/dragonbone_flaming_sword"
				criterion: ""
				id: "6A0BEF623557BF4F"
				title: "Create a Legendary Fire, Ice or Lightning Dragon Weapon"
				type: "advancement"
			}]
			title: "Craft the &eDragon Bone Sword"
			x: 5.5d
			y: 12.5d
		}
		{
			dependencies: ["6BB536E4647E981D"]
			id: "45B32E001E2D2A59"
			rewards: [{
				id: "5090129B02DB9FB0"
				item: "iceandfire:dread_key"
				type: "item"
			}]
			tasks: [{
				advancement: "iceandfire:iceandfire/dragonsteel_weapon"
				criterion: ""
				id: "55DBAC0D7C1174B0"
				title: "Create a Weapon from Dragonsteel"
				type: "advancement"
			}]
			title: "Craft the &bDragonsteel Sword"
			x: 13.5d
			y: 12.5d
		}
		{
			dependencies: ["2895F13A9C99D578"]
			id: "1EFEB5A9C1391220"
			rewards: [
				{
					id: "1369C6DF646B42E3"
					item: "iceandfire:dragonforge_fire_input"
					type: "item"
				}
				{
					id: "19AFEE9103B9379E"
					item: "iceandfire:dragonforge_ice_input"
					type: "item"
				}
				{
					id: "2055A846693266DF"
					item: "iceandfire:dragonforge_lightning_input"
					type: "item"
				}
			]
			tasks: [{
				advancement: "iceandfire:iceandfire/dragon_forge_core"
				criterion: ""
				id: "32618C6556E976F6"
				title: "Create a Dragon Forge Core"
				type: "advancement"
			}]
			title: "Craft the &6Dragon Forge Core"
			x: 9.5d
			y: 12.5d
		}
	]
	title: " &4Ice And Fire"
}
